package com.sbg.ug.optimus_backend.billing.orchastration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.task.TaskExecutor;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class TransactionOrchestratorTest {

    @Mock private TaskExecutor taskExecutor;

    private TransactionOrchestrator orchestrator;

    @BeforeEach
    void setUp() {
        orchestrator = new TransactionOrchestrator();
        // Inject the mocked TaskExecutor using reflection
        ReflectionTestUtils.setField(orchestrator, "taskExecutor", taskExecutor);
    }

    @Test
    void newTransaction_shouldReturnNewTransactionBuilder() {
        // Act
        TransactionOrchestrator.TransactionBuilder builder = orchestrator.newTransaction();

        // Assert
        assertNotNull(builder, "Should return a non-null TransactionBuilder");
        assertNotSame(
                orchestrator.newTransaction(), builder, "Each call should return a new instance");
    }

    @Test
    void newTransaction_shouldReturnBuilderWithDefaultSettings() {
        // Act
        TransactionOrchestrator.TransactionBuilder builder = orchestrator.newTransaction();

        // Assert
        assertNotNull(builder);
        // Verify builder can be used immediately
        TransactionResult result = builder.execute();
        assertTrue(result.success(), "Empty transaction should succeed");
    }

    @Test
    void multipleTransactionBuilders_shouldBeIndependent() throws Exception {
        // Arrange
        DistributedAction<String> action1 = mock(DistributedAction.class);
        DistributedAction<String> action2 = mock(DistributedAction.class);

        when(action1.execute(any())).thenReturn("result1");
        when(action2.execute(any())).thenReturn("result2");

        // Act
        TransactionOrchestrator.TransactionBuilder builder1 = orchestrator.newTransaction();
        TransactionOrchestrator.TransactionBuilder builder2 = orchestrator.newTransaction();

        builder1.addStep("step1", action1);
        builder2.addStep("step2", action2);

        TransactionResult result1 = builder1.execute();
        TransactionResult result2 = builder2.execute();

        // Assert
        assertTrue(result1.success());
        assertTrue(result2.success());
        verify(action1).execute(any(TransactionContext.class));
        verify(action2).execute(any(TransactionContext.class));
    }

    @Test
    void transactionBuilder_shouldHaveAccessToTaskExecutor() {
        // Act
        TransactionOrchestrator.TransactionBuilder builder = orchestrator.newTransaction();

        // Assert - Verify that async execution works (indirectly tests TaskExecutor access)
        assertDoesNotThrow(
                () -> {
                    builder.executeAsync();
                },
                "Should be able to create async execution without throwing");
    }

    @Test
    void orchestrator_asSpringComponent_shouldSupportAutowiring() {
        // This test verifies the @Component and @Autowired annotations work correctly
        // In a real Spring context, the TaskExecutor would be injected automatically

        // Arrange
        TransactionOrchestrator springOrchestrator = new TransactionOrchestrator();
        TaskExecutor realTaskExecutor = mock(TaskExecutor.class);

        // Simulate Spring's dependency injection
        ReflectionTestUtils.setField(springOrchestrator, "taskExecutor", realTaskExecutor);

        // Act
        TransactionOrchestrator.TransactionBuilder builder = springOrchestrator.newTransaction();

        // Assert
        assertNotNull(builder);
        // Verify the injected TaskExecutor is accessible
        assertDoesNotThrow(() -> builder.executeAsync());
    }

    @Test
    void transactionBuilder_shouldMaintainStateAcrossMethodCalls() throws Exception {
        // Arrange
        DistributedAction<String> action = mock(DistributedAction.class);
        when(action.execute(any())).thenReturn("test_result");

        // Act
        TransactionOrchestrator.TransactionBuilder builder = orchestrator.newTransaction();
        builder.setContextValue("key1", "value1");
        builder.withCompensation(false);
        builder.addStep("test_step", action);

        TransactionResult result = builder.execute();

        // Assert
        assertTrue(result.success());
        verify(action).execute(any(TransactionContext.class));
        // Compensation should be disabled, so no compensation calls even if we had failures
    }

    @Test
    void transactionBuilder_shouldAllowMethodChaining() throws Exception {
        // Arrange
        DistributedAction<String> action = mock(DistributedAction.class);
        when(action.execute(any())).thenReturn("chained_result");

        // Act & Assert - Method chaining should work
        TransactionResult result =
                orchestrator
                        .newTransaction()
                        .setContextValue("test_key", "test_value")
                        .withCompensation(true)
                        .addStep("chained_step", action)
                        .execute();

        assertTrue(result.success());
        verify(action).execute(any(TransactionContext.class));
    }

    @Test
    void transactionBuilder_shouldHandleNullTaskExecutor() {
        // Arrange
        TransactionOrchestrator orchestratorWithNullExecutor = new TransactionOrchestrator();
        // Don't inject TaskExecutor (leave it null)

        // Act & Assert
        TransactionOrchestrator.TransactionBuilder builder =
                orchestratorWithNullExecutor.newTransaction();
        assertNotNull(builder);

        // Async execution should fail gracefully with null TaskExecutor
        assertThrows(
                NullPointerException.class,
                () -> {
                    builder.executeAsync().get();
                });
    }
}
